import QRCodeUtils from './QRCodeUtils.js';

/**
 * Specialized Thermal Printer for Repair QR Code Tickets
 * Handles 40x60mm thermal printing with specific layout:
 * - Big QR code on left
 * - Client name under QR code
 * - Phone number under client name
 * - Total price under phone
 */
class RepairThermalPrinter {
  constructor() {
    this.printerWidth = '40mm';
    this.printerHeight = '60mm';
  }

  /**
   * Generate thermal print content for repair QR code
   * @param {Object} repair - Repair data
   * @param {Object} options - Print options (language, etc.)
   * @returns {Promise<string>} HTML content for thermal printing
   */
  async generateQRTicketContent(repair, options = {}) {
    try {
      const { language = 'ar', storeSettings = {} } = options;
      
      // Generate QR code for the repair
      const qrCode = await QRCodeUtils.generateThermalQRCode(repair);
      
      if (!qrCode) {
        throw new Error('Failed to generate QR code');
      }

      const totalPrice = (repair.repairPrice || 0) + (repair.partsPrice || 0);
      const isRTL = language === 'ar';

      const content = `
        <!DOCTYPE html>
        <html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${language}">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Repair QR Ticket</title>
          <style>
            ${this.getThermalQRStyles(language, isRTL)}
          </style>
        </head>
        <body>
          <div class="thermal-qr-ticket">
            <!-- QR Code Section -->
            <div class="qr-section">
              <img src="${qrCode}" alt="Repair QR Code" class="qr-code-large" />
            </div>
            
            <!-- Client Information Section -->
            <div class="client-info-section">
              <div class="client-name">
                ${repair.clientName || 'N/A'}
              </div>
              
              <div class="client-phone">
                ${repair.clientPhone || repair.phone || 'N/A'}
              </div>
              
              <div class="total-price">
                ${this.formatPrice(totalPrice, language)} DZD
              </div>
            </div>
            
            <!-- Store Info Footer -->
            <div class="store-footer">
              <div class="store-name">${storeSettings.storeName || 'iCalDZ'}</div>
              <div class="store-phone">${storeSettings.storePhone || '0551930589'}</div>
            </div>
          </div>
        </body>
        </html>
      `;

      return content;
    } catch (error) {
      console.error('Error generating QR ticket content:', error);
      return null;
    }
  }

  /**
   * Get thermal printing styles for QR tickets
   * @param {string} language - Current language
   * @param {boolean} isRTL - Is right-to-left language
   * @returns {string} CSS styles
   */
  getThermalQRStyles(language, isRTL) {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: ${language === 'ar' ? "'Cairo', 'Arial Unicode MS'" : "'Courier New', monospace"};
        font-size: 12px;
        line-height: 1.2;
        color: #000;
        background: white;
        width: 40mm;
        height: 60mm;
        padding: 2mm;
        direction: ${isRTL ? 'rtl' : 'ltr'};
      }

      .thermal-qr-ticket {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
      }

      .qr-section {
        text-align: center;
        margin-bottom: 2mm;
      }

      .qr-code-large {
        width: 25mm;
        height: 25mm;
        border: 1px solid #000;
      }

      .client-info-section {
        text-align: center;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 1mm;
      }

      .client-name {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 1mm;
        word-wrap: break-word;
        max-width: 35mm;
      }

      .client-phone {
        font-size: 12px;
        font-weight: normal;
        margin-bottom: 1mm;
        direction: ltr;
      }

      .total-price {
        font-size: 16px;
        font-weight: bold;
        border: 2px solid #000;
        padding: 1mm;
        background: #f0f0f0;
        margin-top: 1mm;
      }

      .store-footer {
        text-align: center;
        font-size: 8px;
        margin-top: 2mm;
        border-top: 1px solid #000;
        padding-top: 1mm;
      }

      .store-name {
        font-weight: bold;
        margin-bottom: 0.5mm;
      }

      .store-phone {
        direction: ltr;
      }

      /* Print-specific styles */
      @media print {
        body {
          margin: 0;
          padding: 2mm;
        }
        
        .thermal-qr-ticket {
          page-break-inside: avoid;
        }
      }

      /* RTL specific adjustments */
      ${isRTL ? `
        .client-phone,
        .store-phone {
          direction: ltr;
          text-align: center;
        }
      ` : ''}
    `;
  }

  /**
   * Format price according to language
   * @param {number} price - Price to format
   * @param {string} language - Language code
   * @returns {string} Formatted price
   */
  formatPrice(price, language) {
    const numPrice = parseFloat(price) || 0;
    
    if (language === 'ar') {
      return numPrice.toLocaleString('ar-DZ');
    } else if (language === 'fr') {
      return numPrice.toLocaleString('fr-DZ');
    } else {
      return numPrice.toLocaleString('en-US');
    }
  }

  /**
   * Print QR ticket directly to thermal printer
   * @param {Object} repair - Repair data
   * @param {Object} options - Print options
   */
  async printQRTicket(repair, options = {}) {
    try {
      const content = await this.generateQRTicketContent(repair, options);
      
      if (!content) {
        throw new Error('Failed to generate ticket content');
      }

      // Create hidden iframe for printing
      const iframe = document.createElement('iframe');
      iframe.style.position = 'absolute';
      iframe.style.left = '-9999px';
      iframe.style.width = '40mm';
      iframe.style.height = '60mm';
      
      document.body.appendChild(iframe);
      
      const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
      iframeDoc.open();
      iframeDoc.write(content);
      iframeDoc.close();

      // Wait for content to load
      await new Promise(resolve => {
        iframe.onload = resolve;
        setTimeout(resolve, 1000); // Fallback timeout
      });

      // Print the iframe
      iframe.contentWindow.focus();
      iframe.contentWindow.print();

      // Clean up after printing
      setTimeout(() => {
        document.body.removeChild(iframe);
      }, 1000);

      return true;
    } catch (error) {
      console.error('Error printing QR ticket:', error);
      return false;
    }
  }

  /**
   * Generate printable window for QR ticket
   * @param {Object} repair - Repair data
   * @param {Object} options - Print options
   */
  async openPrintWindow(repair, options = {}) {
    try {
      const content = await this.generateQRTicketContent(repair, options);
      
      if (!content) {
        throw new Error('Failed to generate ticket content');
      }

      const printWindow = window.open('', '_blank', 'width=400,height=600');
      printWindow.document.write(content);
      printWindow.document.close();
      
      // Auto-print when window loads
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };

      return true;
    } catch (error) {
      console.error('Error opening print window:', error);
      return false;
    }
  }
}

// Export singleton instance
export default new RepairThermalPrinter();
